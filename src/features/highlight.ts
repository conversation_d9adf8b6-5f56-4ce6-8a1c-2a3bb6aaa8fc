/**
 * 🎯 Lucid Highlight System - 简单的选择高亮系统
 *
 * 设计原则：能跑、好改、文件少
 *
 * 功能特性：
 * ✅ 手动选择高亮 (Shift + 选择)
 * ✅ 基本存储和样式管理
 * ✅ 统一错误处理和配置管理
 * ✅ 性能优化和内存管理
 *
 * 使用示例：
 * ```typescript
 * const highlighter = new LucidHighlight();
 * await highlighter.initialize();
 * ```
 */

import { contentErrorHandler, ErrorSeverity } from '../utils/error-handler';
import { getTextConfig, getPerformanceConfig, getStorageConfig } from '../config/highlight-config';

// === 类型定义 ===
interface HighlightConfig {
  effects?: string[];
  enabled?: boolean;
  minWordLength?: number;
  maxWordLength?: number;
  blacklistedWords?: string[];
}

// === 存储接口 ===
interface StorageData {
  [key: string]: unknown;
}

interface ExtensionStorage {
  get(key: string): Promise<StorageData>;
  set(data: StorageData): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  // 新增错误恢复和健康检查
  isHealthy(): Promise<boolean>;
  backup(): Promise<void>;
  restore(): Promise<void>;
}

// === 内存存储实现（作为fallback） ===
class MemoryStorage implements ExtensionStorage {
  private data = new Map<string, unknown>();
  private backupData = new Map<string, unknown>();

  async get(key: string): Promise<StorageData> {
    const result = this.data.get(key);
    return result ? { [key]: result } : {};
  }

  async set(data: StorageData): Promise<void> {
    Object.entries(data).forEach(([key, value]) => {
      this.data.set(key, value);
    });
  }

  async remove(key: string): Promise<void> {
    this.data.delete(key);
  }

  async clear(): Promise<void> {
    this.data.clear();
  }

  async isHealthy(): Promise<boolean> {
    return true; // 内存存储总是健康的
  }

  async backup(): Promise<void> {
    this.backupData = new Map(this.data);
  }

  async restore(): Promise<void> {
    this.data = new Map(this.backupData);
  }
}

// === 高亮核心类 ===
export class LucidHighlight {
  private config: Required<HighlightConfig> = {
    effects: ["lu-gradient", "lu-underline"],
    enabled: true,
    minWordLength: 2,
    maxWordLength: 30,
    blacklistedWords: ["the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"]
  };

  private wordCounts = new Map<string, number>();
  private storage: ExtensionStorage | null = null;
  private eventListeners: Array<{ element: EventTarget, event: string, handler: EventListener }> = [];
  private debounceTimer: number | null = null;

  constructor(config: HighlightConfig = {}) {
    this.config = { ...this.config, ...config };
    this.storage = null;
  }

  // === 存储系统 ===
  private async initStorageAsync(): Promise<ExtensionStorage> {
    console.log('⚙️ [highlight-storage|INIT] 存储系统初始化中...');

    const waitForExtensionAPI = (): Promise<chrome.storage.LocalStorageArea | null> => {
      return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 20;

        const checkAPI = () => {
          attempts++;
          const browserAPI = (globalThis as Record<string, unknown>).browser as typeof chrome | undefined;
          const chromeAPI = (globalThis as Record<string, unknown>).chrome as typeof chrome | undefined;
          const api = browserAPI?.storage?.local || chromeAPI?.storage?.local;

          if (api) {
            console.log('✅ [highlight-storage|API] 扩展存储API可用');
            resolve(api);
          } else if (attempts >= maxAttempts) {
            console.warn('⚠️ [highlight-storage|FALLBACK] 扩展存储API不可用，回退到localStorage');
            resolve(null);
          } else {
            setTimeout(checkAPI, 100);
          }
        };

        checkAPI();
      });
    };

    const api = await waitForExtensionAPI();

    if (!api) {
      return {
        get: async (key: string) => {
          try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : {};
          } catch (error) {
            console.error('localStorage get error:', error);
            return {};
          }
        },
        set: async (data: StorageData) => {
          try {
            for (const [key, value] of Object.entries(data)) {
              localStorage.setItem(key, JSON.stringify(value));
            }
          } catch (error) {
            console.error('localStorage set error:', error);
          }
        },
        remove: async (key: string) => {
          try {
            localStorage.removeItem(key);
          } catch (error) {
            console.error('localStorage remove error:', error);
          }
        },
        clear: async () => {
          try {
            localStorage.clear();
          } catch (error) {
            console.error('localStorage clear error:', error);
          }
        },
        isHealthy: async () => {
          try {
            const testKey = '__lucid_health_check__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
          } catch {
            return false;
          }
        },
        backup: async () => {
          try {
            const backupData: Record<string, string> = {};
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && key.startsWith('lucid-')) {
                backupData[key] = localStorage.getItem(key) || '';
              }
            }
            localStorage.setItem('__lucid_backup__', JSON.stringify(backupData));
          } catch (error) {
            console.error('localStorage backup error:', error);
          }
        },
        restore: async () => {
          try {
            const backupData = localStorage.getItem('__lucid_backup__');
            if (backupData) {
              const data = JSON.parse(backupData);
              Object.entries(data).forEach(([key, value]) => {
                if (key !== '__lucid_backup__') {
                  localStorage.setItem(key, value as string);
                }
              });
            }
          } catch (error) {
            console.error('localStorage restore error:', error);
          }
        }
      };
    }

    console.log('⚙️ [highlight-storage|MODE] 使用扩展存储API');

    // 扩展存储API包装，添加健康检查和备份恢复功能
    return {
      get: async (key: string) => api.get(key),
      set: async (data: StorageData) => api.set(data),
      remove: async (key: string) => api.remove(key),
      clear: async () => api.clear(),
      isHealthy: async () => {
        try {
          const testKey = '__lucid_health_check__';
          await api.set({ [testKey]: 'test' });
          await api.remove(testKey);
          return true;
        } catch {
          return false;
        }
      },
      backup: async () => {
        try {
          const allData = await api.get(null);
          await api.set({ '__lucid_backup__': allData });
        } catch (error) {
          console.error('Extension storage backup error:', error);
        }
      },
      restore: async () => {
        try {
          const result = await api.get('__lucid_backup__');
          if (result && result['__lucid_backup__']) {
            await api.set(result['__lucid_backup__']);
          }
        } catch (error) {
          console.error('Extension storage restore error:', error);
        }
      }
    };
  }

  /**
   * 初始化存储系统，包含错误恢复机制
   */
  private async initStorageWithFallback(): Promise<ExtensionStorage> {
    try {
      const storage = await this.initStorageAsync();
      const isHealthy = await storage.isHealthy();

      if (!isHealthy) {
        console.warn('⚠️ [highlight-storage|WARN] Storage unhealthy, attempting recovery...');
        await storage.restore();

        // 再次检查健康状态
        const isHealthyAfterRestore = await storage.isHealthy();
        if (!isHealthyAfterRestore) {
          console.warn('⚠️ [highlight-storage|WARN] Storage still unhealthy, using memory fallback');
          return new MemoryStorage();
        }
      }

      return storage;
    } catch (error) {
      console.error('❌ [highlight-storage|ERROR] Storage initialization failed, using memory fallback:', error);
      return new MemoryStorage();
    }
  }

  private async loadWordCounts(): Promise<void> {
    try {
      if (!this.storage) {
        console.error('Storage not initialized');
        this.wordCounts = new Map();
        return;
      }

      const result = await this.storage.get("lucid-word-counts");
      let data = {};

      if (result && typeof result === 'object') {
        if (result["lucid-word-counts"]) {
          data = result["lucid-word-counts"];
        } else if (Object.keys(result).some(key => typeof result[key] === 'number')) {
          data = result;
        }
      }

      this.wordCounts = new Map(Object.entries(data));
      console.log(`✅ [highlight-storage|LOAD] 从存储加载${this.wordCounts.size}个词汇`);

      if (this.wordCounts.size === 0) {
        console.log('ℹ️ [highlight-storage|EMPTY] 未找到现有数据');
      }
    } catch (error) {
      console.error('Error loading word counts:', error);
      this.wordCounts = new Map();
    }
  }

  private async saveWordCounts(): Promise<void> {
    try {
      if (!this.storage) {
        console.warn('Storage not available, skipping save');
        return;
      }

      await this.storage.set({ "lucid-word-counts": Object.fromEntries(this.wordCounts) });
    } catch (error) {
      // Silently ignore extension context invalidation errors during development
      if (error instanceof Error && error.message.includes('Extension context invalidated')) {
        console.warn('🔄 Extension context invalidated, skipping word count save');
        return;
      }
      console.error('Error saving word counts:', error);
    }
  }

  // === 样式计算 ===
  private calculateLevel(count: number): number {
    if (count <= 0) return 1;
    return Math.min(Math.ceil(count / 2), 5);
  }

  private generateClasses(count: number, isDark: boolean): string[] {
    const level = this.calculateLevel(count);
    const classes = [`lu-level-${level}`, ...this.config.effects];
    if (isDark) classes.push("lu-dark-text");
    return classes;
  }

  private isDarkText(element: Element): boolean {
    const color = window.getComputedStyle(element).color;
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (!rgbMatch) return false;

    const [, r, g, b] = rgbMatch.map(Number);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance < 0.5;
  }

  private containsMultipleWords(text: string): boolean {
    return /\s/.test(text) || /[.,;:!?]/.test(text);
  }

  // === DOM 操作 ===
  private createHighlightElement(text: string, count: number, isDark: boolean): HTMLElement {
    const element = document.createElement("lucid-highlight");
    element.textContent = text;
    element.dataset.word = text.toLowerCase();
    element.dataset.count = count.toString();

    const classes = this.generateClasses(count, isDark);
    element.classList.add('lucid-highlight', ...classes);

    return element;
  }



  // === 事件处理 ===
  private async handleHighlight(): Promise<void> {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const range = selection.getRangeAt(0);
    const text = range.toString().trim();
    const word = text.toLowerCase();

    // 验证选择
    if (text.length < this.config.minWordLength) return;
    if (text.length > this.config.maxWordLength) return;
    if (this.containsMultipleWords(text)) return;
    if (this.config.blacklistedWords.includes(word)) return;

    try {
      // 增加计数
      const currentCount = this.wordCounts.get(word) || 0;
      this.wordCounts.set(word, currentCount + 1);

      // 保存到存储
      await this.saveWordCounts();

      // 高亮所有匹配的单词
      this.highlightAllOccurrences(word);

      // 清除选择
      selection.removeAllRanges();
    } catch (error) {
      console.error("Highlight error:", error);
    }
  }

  private createEventHandler(): EventListener {
    return (event: Event) => {
      if (!this.config.enabled) return;

      const keyEvent = event as KeyboardEvent;

      if (keyEvent.key === 'Shift' && !keyEvent.ctrlKey && !keyEvent.altKey && !keyEvent.metaKey) {
        if (this.debounceTimer) clearTimeout(this.debounceTimer);

        // 立即处理高亮，不延迟
        this.handleHighlight();
      }
    };
  }

  private createContextMenuHandler(): EventListener {
    return async (event: Event) => {
      const mouseEvent = event as MouseEvent;
      const target = mouseEvent.target as HTMLElement;

      if (target.tagName === "LUCID-HIGHLIGHT" || target.tagName === "lucid-highlight" || target.classList.contains('lucid-highlight')) {
        mouseEvent.preventDefault();
        await this.removeHighlight(target);
      }
    };
  }

  // === 优化的高亮系统 ===
  private highlightAllOccurrences(word: string): void {
    try {
      const count = this.wordCounts.get(word) || 1;
      const performanceConfig = getPerformanceConfig();

      // 使用批量处理和视窗限制优化性能
      const visibleTextNodes = this.getVisibleTextNodes();

      this.processBatchedHighlights(visibleTextNodes, word, count, performanceConfig.BATCH_SIZE);
    } catch (error) {
      contentErrorHandler.runtime(error as Error, {
        word,
        method: 'highlightAllOccurrences'
      }, ErrorSeverity.MEDIUM);
    }
  }

  /**
   * 获取视窗内的文本节点，减少不必要的DOM遍历
   */
  private getVisibleTextNodes(): Text[] {
    const viewport = this.getViewportBounds();
    const textNodes: Text[] = [];

    // 只遍历可能在视窗内的元素
    const visibleElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, article, section');

    visibleElements.forEach(element => {
      const rect = element.getBoundingClientRect();

      // 检查元素是否在扩展的视窗范围内（包含一些缓冲区）
      if (this.isElementInViewport(rect, viewport)) {
        const walker = document.createTreeWalker(
          element,
          NodeFilter.SHOW_TEXT,
          {
            acceptNode: (node) => {
              // 过滤掉已排除的元素内的文本节点
              return this.isInExcludedElement(node) ?
                NodeFilter.FILTER_REJECT :
                NodeFilter.FILTER_ACCEPT;
            }
          }
        );

        let node;
        while (node = walker.nextNode()) {
          textNodes.push(node as Text);
        }
      }
    });

    return textNodes;
  }

  /**
   * 获取视窗边界，包含缓冲区
   */
  private getViewportBounds() {
    const buffer = 500; // 500px缓冲区
    return {
      top: window.scrollY - buffer,
      bottom: window.scrollY + window.innerHeight + buffer,
      left: window.scrollX - buffer,
      right: window.scrollX + window.innerWidth + buffer
    };
  }

  /**
   * 检查元素是否在视窗范围内
   */
  private isElementInViewport(rect: DOMRect, viewport: { top: number; bottom: number; left: number; right: number }): boolean {
    const elementTop = rect.top + window.scrollY;
    const elementBottom = elementTop + rect.height;
    const elementLeft = rect.left + window.scrollX;
    const elementRight = elementLeft + rect.width;

    return !(elementBottom < viewport.top ||
             elementTop > viewport.bottom ||
             elementRight < viewport.left ||
             elementLeft > viewport.right);
  }

  /**
   * 批量处理高亮，使用requestIdleCallback避免阻塞UI
   */
  private processBatchedHighlights(textNodes: Text[], word: string, count: number, batchSize: number): void {
    const regex = new RegExp(`\\b${this.escapeRegExp(word)}\\b`, 'gi');
    let currentIndex = 0;

    const processBatch = () => {
      const endIndex = Math.min(currentIndex + batchSize, textNodes.length);
      const batch = textNodes.slice(currentIndex, endIndex);

      // 处理当前批次
      batch.forEach(textNode => {
        const text = textNode.textContent || '';
        if (regex.test(text)) {
          this.highlightWordInTextNode(textNode, word, count);
        }
      });

      currentIndex = endIndex;

      // 如果还有更多节点需要处理，安排下一批
      if (currentIndex < textNodes.length) {
        if ('requestIdleCallback' in window) {
          requestIdleCallback(processBatch, { timeout: 100 });
        } else {
          // fallback for browsers without requestIdleCallback
          setTimeout(processBatch, 0);
        }
      }
    };

    // 开始处理第一批
    processBatch();
  }

  private highlightWordInTextNode(textNode: Text, word: string, count: number): void {
    const parent = textNode.parentNode;
    if (!parent || this.isInExcludedElement(textNode)) return;

    const text = textNode.textContent || '';
    const regex = new RegExp(`\\b${this.escapeRegExp(word)}\\b`, 'gi');

    let lastIndex = 0;
    let match;
    const fragments: Node[] = [];

    while ((match = regex.exec(text)) !== null) {
      // Add text before match
      if (match.index > lastIndex) {
        fragments.push(document.createTextNode(text.substring(lastIndex, match.index)));
      }

      // Create highlight element
      const highlightElement = this.createHighlightElement(match[0], count, this.isDarkText(parent as Element));
      fragments.push(highlightElement);

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      fragments.push(document.createTextNode(text.substring(lastIndex)));
    }

    // Replace the original text node with fragments
    if (fragments.length > 0) {
      fragments.forEach(fragment => parent.insertBefore(fragment, textNode));
      parent.removeChild(textNode);
    }
  }

  private isInExcludedElement(node: Node): boolean {
    const textConfig = getTextConfig();
    let parent = node.parentElement;

    while (parent) {
      // 检查排除的元素标签
      if (textConfig.EXCLUDED_ELEMENTS.includes(parent.tagName as any)) {
        return true;
      }

      // 检查排除的类名
      if (parent.classList) {
        for (const excludedClass of textConfig.EXCLUDED_CLASSES) {
          if (parent.classList.contains(excludedClass)) {
            return true;
          }
        }
      }

      parent = parent.parentElement;
    }
    return false;
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // === 公共接口 ===
  async initialize(): Promise<void> {
    try {
      // 使用增强的存储初始化机制
      try {
        this.storage = await this.initStorageWithFallback();
      } catch (error) {
        console.error('❌ [highlight-storage|ERROR] Storage initialization failed completely:', error);
        this.storage = new MemoryStorage();
      }

      await this.loadWordCounts();

      // 注册自定义元素
      if (typeof customElements !== 'undefined' && customElements && !customElements.get('lucid-highlight')) {
        customElements.define('lucid-highlight', class extends HTMLElement { });
      } else if (typeof customElements === 'undefined') {
        console.warn('customElements not available, skipping custom element registration');
      }

      // 设置事件处理
      const keyHandler = this.createEventHandler();
      const contextHandler = this.createContextMenuHandler();

      document.addEventListener("keydown", keyHandler);
      document.addEventListener("contextmenu", contextHandler);

      this.eventListeners.push(
        { element: document, event: "keydown", handler: keyHandler },
        { element: document, event: "contextmenu", handler: contextHandler }
      );

      console.log('✅ [highlight-system|STARTUP] Lucid高亮系统初始化完成');
    } catch (error) {
      console.error('Error during initialization:', error);
      throw error;
    }
  }

  destroy(): void {
    // 清理事件监听器
    this.eventListeners.forEach(({ element, event, handler }) => {
      try {
        element.removeEventListener(event, handler);
      } catch (error) {
        console.warn('Error removing event listener:', error);
      }
    });
    this.eventListeners = [];

    // 清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    // 清理存储引用
    this.storage = null;

    // 清理词汇计数
    this.wordCounts.clear();

    // 清理全局引用
    if (typeof window !== 'undefined') {
      const globalWindow = window as unknown as Record<string, unknown>;

      if (globalWindow.lucidHighlighter === this) {
        delete globalWindow.lucidHighlighter;
      }

      // 清理全局函数
      if (globalWindow.clearLucidHighlights) {
        delete globalWindow.clearLucidHighlights;
      }
    }

    console.log("✅ [highlight-system|CLEANUP] Lucid highlight system destroyed completely");
  }

  async removeHighlight(element: HTMLElement): Promise<void> {
    const word = element.dataset.word;
    if (!word) return;

    // 重置计数
    this.wordCounts.delete(word);

    // 移除元素
    const parent = element.parentNode;
    if (parent) {
      const textNode = document.createTextNode(element.textContent || "");
      parent.replaceChild(textNode, element);
    }

    // 保存更改
    await this.saveWordCounts();
  }

  async clearAll(): Promise<void> {
    this.wordCounts.clear();

    // 移除所有高亮元素
    const highlights = document.querySelectorAll("lucid-highlight");
    highlights.forEach(element => {
      const parent = element.parentNode;
      if (parent) {
        const textNode = document.createTextNode(element.textContent || "");
        parent.replaceChild(textNode, element);
      }
    });

    await this.saveWordCounts();
  }

  async getWordCount(word: string): Promise<number> {
    return this.wordCounts.get(word.toLowerCase()) || 0;
  }

  getHighlightedWords(): string[] {
    return Array.from(this.wordCounts.keys());
  }

  getHighlightStats(): { totalWords: number, totalHighlights: number } {
    const totalHighlights = document.querySelectorAll("lucid-highlight").length;
    return {
      totalWords: this.wordCounts.size,
      totalHighlights
    };
  }
}

// Global helper functions for console debugging
declare global {
  interface Window {
    lucidHighlighter: LucidHighlight | null;
    clearLucidHighlights: () => Promise<void>;
  }
}

// Export global helper function
if (typeof window !== 'undefined') {
  window.clearLucidHighlights = async () => {
    if (window.lucidHighlighter) {
      await window.lucidHighlighter.clearAll();
      console.log('All Lucid highlights cleared!');
    } else {
      console.warn('Lucid highlighter not found. Make sure it\'s initialized.');
    }
  };
}